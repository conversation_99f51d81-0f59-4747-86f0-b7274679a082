import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { User } from '@prisma/client';
import {
  comparePassword,
  hashPassword,
} from '@shared-library/common/utils/bycrypt';
import { generateCryptoSecret } from '@shared-library/common/utils/crypto';
import {
  generateTOTP,
  initializeTOTP,
  verifyTOTP,
} from '@shared-library/common/utils/otpauth';
import { generateRecoveryCodes } from '@shared-library/common/utils/recoveryCode';
import { PrismaService } from 'src/persistence/prisma/prisma.service';
import { FlyPreviewManager } from 'src/preview/fly-preview-manager';
import { v4 as uuidv4 } from 'uuid';
import { UsersService } from '../users/users.service';
import { IChangePassword } from './interfaces/changep-password.interface';
import { IEmailLogin } from './interfaces/email-login.interface';
import { LoginResponse } from './interfaces/login-response';
import { IUserCreate } from './interfaces/user-create.interface';
import { IVerifyEmail } from './interfaces/verify-email.interface';
import { TokenService } from './token.service';

@Injectable()
export class AuthService {
  private flyPreviewManager: FlyPreviewManager;

  constructor(
    protected usersService: UsersService,
    protected tokenService: TokenService,
    protected prisma: PrismaService,
  ) {
    // Initialize FlyPreviewManager if environment variables are available
    const apiKey = process.env.FLY_API_TOKEN;
    const dockerImage = process.env.DOCKER_IMAGE;
    const orgSlug = process.env.FLY_ORG_SLUG;

    if (apiKey && dockerImage && orgSlug) {
      this.flyPreviewManager = new FlyPreviewManager(
        apiKey,
        dockerImage,
        orgSlug,
      );
    }
  }

  getHello(): string {
    return 'Hello from the shared AuthService!';
  }

  /**
   *
   * @param user
   * @returns
   */
  async signup(user: IUserCreate): Promise<any> {
    const hashedPassword = await hashPassword(user.password);
    user.password = hashedPassword;

    // Create fly app for the user if FlyPreviewManager is available
    let flyAppName: string | null = null;
    if (this.flyPreviewManager) {
      try {
        flyAppName = uuidv4(); // Use UUID for unique app names
        await this.flyPreviewManager.createApp(flyAppName);
        console.log(
          `✅ Created fly app: ${flyAppName} for user: ${user.email}`,
        );
      } catch (error) {
        console.error(
          `❌ Failed to create fly app for user ${user.email}:`,
          error,
        );
        // Continue with user creation even if fly app creation fails
        flyAppName = null;
      }
    }

    const newUser: User = await this.usersService.create(user, flyAppName);

    const code = await this.sendVerificationCode(newUser);

    return {
      code,
      message: `User ${user.email} created successfully`,
    };
  }

  /**
   *
   * @param email
   * @returns User | null
   */
  async getUserByEmail(email: string): Promise<User | null> {
    return await this.usersService.getUserByEmail(email);
  }

  async findUser(id: number): Promise<User | null> {
    return await this.usersService.find(id);
  }

  /**
   *
   * @param user
   * @returns
   */
  async sendVerificationCode(user: User): Promise<string> {
    const secret = user.verification_secret;
    const code = await generateTOTP(secret, user.email);
    console.log({ code });
    //TODO : Implement Engagespot

    return code;
  }

  /**
   *
   * @param data
   * @returns
   */
  async verifyEmail(data: IVerifyEmail): Promise<LoginResponse> {
    const user = await this.usersService.getUserByEmail(data.email);
    if (!user)
      throw new NotFoundException(`User with email ${data.email} not found`);

    const secret = user.verification_secret;
    const code = data.code;

    if (!(await verifyTOTP(secret, user.email, code)))
      throw new NotFoundException(
        `Invalid verification code for user ${data.email}`,
      );

    user.is_email_verified = true;
    user.email_verified_at = new Date();
    user.last_login = new Date();
    await this.usersService.update(user);

    const { accessToken, refreshToken } =
      await this.tokenService.generateTokens(user);

    return <LoginResponse>{
      message: `User ${data.email} verified successfully`,
      data: { accessToken, refreshToken, furtherAction: null },
    };
  }

  /**
   *
   * @param data
   * @returns
   */
  async resendVerificationCode(data: Partial<IVerifyEmail>): Promise<any> {
    const user = await this.usersService.getUserByEmail(data.email);
    if (!user)
      throw new NotFoundException(`User with email ${data.email} not found`);

    const code = await this.sendVerificationCode(user);

    //TODO : Implement Rate Limiting for resend verification code

    return {
      message: `Verification code for user ${data.email} resent successfully`,
      code,
    };
  }

  /**
   *
   * @param data
   * @returns
   * @throws NotFoundException
   */
  async loginWithEmail(data: IEmailLogin): Promise<LoginResponse> {
    const { email, password } = data;

    const user = await this.usersService.getUserByEmail(email);

    if (!user) throw new UnauthorizedException('Invalid email or password');

    if (!user.password_hash)
      throw new UnprocessableEntityException(
        `This account was created using Google or another OAuth provider. Please log in with the corresponding provider.`,
      );

    if (!user.is_email_verified)
      throw new UnprocessableEntityException(`User ${email} is not verified`);

    const isPasswordValid = await comparePassword(password, user.password_hash);
    if (!isPasswordValid)
      throw new UnauthorizedException('Invalid email or password');

    await this.usersService.update({ ...user, last_login: new Date() });

    const tokenResult = user.is_two_factor_enabled
      ? await this.tokenService.generateTwoFactorAuthToken(user)
      : await this.tokenService.generateTokens(user);

    const workspaces = await this.prisma.workspace.findFirst({
      where: {
        owner_id: user.id,
      },
    });
    const firstWorkspace = await this.prisma.onboardingProgress.findUnique({
      where: { workspaceId: workspaces.id },
    });

    return <LoginResponse>{
      message: `User ${email} logged in successfully`,
      data: {
        workspace: firstWorkspace,
        accessToken: tokenResult.accessToken,
        refreshToken: user.is_two_factor_enabled
          ? null
          : tokenResult.refreshToken,
        furtherAction: user.is_two_factor_enabled
          ? 'verifyTwoFactorAuth'
          : null,
      },
    };
  }

  /**
   *
   * @param user
   * @returns
   */
  async generateTokens(user: User): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    return await this.tokenService.generateTokens(user);
  }

  /**
   *
   * @param user
   * @param data
   * @returns
   */
  async changePassword(user: User, data: IChangePassword): Promise<void> {
    const isPasswordValid = await comparePassword(
      data.currentPassword,
      user.password_hash,
    );
    if (!isPasswordValid)
      throw new UnauthorizedException('Invalid current password');

    const hashedPassword = await hashPassword(data.newPassword);

    user.password_hash = hashedPassword;
    await this.usersService.update(user);

    return;
  }

  /**
   *
   * @param user
   * @returns
   */
  async logoutAllDevices(user: User): Promise<void> {
    await this.tokenService.revokeAllTokens(user);
    return;
  }

  /**
   *
   * @param user
   * @returns
   */
  async getTwoFactorAuthQRCode(user: User): Promise<any> {
    const totp = await this.getTotpAuth(user);
    const freeText = totp.toString();
    return {
      message: 'QR code generated successfully',
      data: { freeText },
    };
  }

  /**
   *
   * @param user
   * @returns
   */
  async getTotpAuth(user: User): Promise<any> {
    const secret = user.two_factor_secret || generateCryptoSecret(32);
    await this.usersService.update({ ...user, two_factor_secret: secret });

    return initializeTOTP(secret, user.email);
  }

  /**
   *
   * @param user
   * @param code
   * @returns
   */
  async enableTwoFactorAuth(user: User, code: string): Promise<any> {
    const isVerified = await verifyTOTP(
      user.two_factor_secret,
      user.email,
      code,
    );

    if (!isVerified)
      throw new UnprocessableEntityException(
        'Oops Invalid verification code passed',
      );

    const recoveryCodes = await this.generateRecoveryCodes(user);

    user.is_two_factor_enabled = true;
    await this.usersService.update(user);

    return {
      message:
        'Two-factor authentication enabled successfully. Please save these recovery codes in a safe place.',
      data: recoveryCodes,
    };
  }

  /**
   *
   * @param user
   * @returns
   */
  async generateRecoveryCodes(user: User): Promise<string[]> {
    await this.usersService.deleteRecoveryCodes(user);

    const codes = generateRecoveryCodes(10, 8);
    await this.usersService.saveRecoveryCodes(user, codes);

    return codes;
  }

  /**
   *
   * @param user
   * @returns
   */
  async disableTwoFactorAuth(user: User): Promise<void> {
    user.is_two_factor_enabled = false;
    await this.usersService.update(user);

    await this.usersService.deleteRecoveryCodes(user);

    return;
  }

  /**
   *
   * @param user
   * @returns
   */
  async verifyTwoFactorAuth(user: User, code: string): Promise<LoginResponse> {
    const isVerified = await verifyTOTP(
      user.two_factor_secret,
      user.email,
      code,
    );
    if (!isVerified)
      throw new UnprocessableEntityException(
        'Oops Invalid verification code passed',
      );

    const { accessToken, refreshToken } =
      await this.tokenService.generateTokens(user);

    user.last_login = new Date();
    await this.usersService.update(user);

    return <LoginResponse>{
      message: 'Two-factor authentication enabled successfully.',
      data: { accessToken, refreshToken, furtherAction: null },
    };
  }

  /**
   * Login with recovery code
   * @param user
   * @param recoveryCode
   * @returns
   */
  async loginWithRecoveryCode(
    user: User,
    recoveryCode: string,
  ): Promise<LoginResponse> {
    const recoveryCodeObj = await this.usersService.validateRecoveryCode(
      user,
      recoveryCode,
    );
    this.usersService.deleteRecoveryCode(recoveryCodeObj);

    //TODO : Inform user (recoverycode's Owner) that the recovery code is Used

    const { accessToken, refreshToken } =
      await this.tokenService.generateTokens(user);

    user.last_login = new Date();
    await this.usersService.update(user);

    return <LoginResponse>{
      message:
        'Two-factor authentication enabled successfully. Please save these recovery codes in a safe place.',
      data: { accessToken, refreshToken, furtherAction: null },
    };
  }

  /**
   * Creates or finds a user using AuthService logic.
   */
  async createOrFindUser(userInfo: any) {
    return await this.usersService.createOrFindUser(userInfo);
  }

  async me(userId: number): Promise<any> {
    const user = await this.usersService.find(userId);
    if (!user) throw new NotFoundException(`User not found`);

    const profileData = await this.usersService.getUserProfile(user);

    const workspaces = await this.prisma.workspace.findMany({
      where: {
        owner_id: userId,
      },
      orderBy: {
        created_at: 'asc',
      },
    });

    return {
      id: user.id,
      email: user.email,
      is_email_verified: user.is_email_verified,
      is_active: user.is_active,
      is_two_factor_enabled: user.is_two_factor_enabled,
      ...profileData,
      workspaces,
    };
  }
}
