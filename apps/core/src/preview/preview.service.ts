import { Injectable, Logger } from '@nestjs/common';
import { FlyPreviewManager } from './fly-preview-manager';
import { PrismaService } from 'src/persistence/prisma/prisma.service';

@Injectable()
export class PreviewService {
  private readonly logger = new Logger(PreviewService.name);
  private flyManager: FlyPreviewManager;

  constructor(private prisma: PrismaService) {
    const apiKey = process.env.FLY_API_TOKEN;
    const dockerImage = process.env.DOCKER_IMAGE;
    const orgSlug = process.env.FLY_ORG_SLUG;

    if (!apiKey) {
      throw new Error('FLY_API_TOKEN environment variable is required');
    }

    if (!dockerImage) {
      throw new Error('DOCKER_IMAGE environment variable is required');
    }

    if (!orgSlug) {
      throw new Error('FLY_ORG_SLUG environment variable is required');
    }

    this.flyManager = new FlyPreviewManager(apiKey, dockerImage, orgSlug);
    this.logger.log('PreviewService initialized with Fly.io integration');
  }

  // NOTE: UserMachine model exists in schema but needs migration before single machine methods work
  // Run: pnpx prisma migrate dev --name add_user_machine_tracking
  // Then: pnpx prisma generate

  async createPreview(files: Record<string, string>) {
    this.logger.log('Starting preview creation process with Fly.io');

    try {
      // Step 1: Create Fly.io app with machine (2-3 seconds)
      this.logger.log('Creating Fly.io app with machine...');

      const createResult = await this.flyManager.createPreviewApp();

      const { machineId, appId, appUrl, appName } = createResult;

      // Step 2: Files are already in the correct format
      this.logger.log(
        `Processing ${Object.keys(files).length} files for injection`,
      );

      // Validate files
      if (!files || Object.keys(files).length === 0) {
        throw new Error('No files provided for preview');
      }

      // Step 3: Inject code into running machine
      this.logger.log('Injecting code into machine...');
      await this.flyManager.injectCode(appUrl, files);
      this.logger.log('Code injection completed successfully');

      // Step 4: Monitor for runtime errors (separate call)
      // const runtimeErrors = await this.flyManager.monitorRuntimeErrors(appName);

      // if (runtimeErrors.length > 0) {
      //   this.logger.warn(
      //     `Runtime errors detected: ${runtimeErrors.length} errors`,
      //   );
      // }

      const result = {
        serviceId: machineId, // For compatibility with existing frontend
        appId,
        previewUrl: appUrl,
        wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
        filesCount: Object.keys(files).length,
        provider: 'fly',
        // runtimeErrors: runtimeErrors,
        // hasRuntimeErrors: runtimeErrors.length > 0,
        runtimeErrors: [],
        hasRuntimeErrors: false,
      };

      this.logger.log(`Preview created successfully: ${result.previewUrl}`);
      return result;
    } catch (error) {
      this.logger.error('Failed to create preview:', error);
      throw new Error(`Preview creation failed: ${error.message}`);
    }
  }

  /**
   * OPTIMIZED: Create preview using user's persistent app
   */
  async createPreviewForUser(
    files: Record<string, string>,
    userId: number,
  ): Promise<any> {
    this.logger.log(`Starting optimized preview creation for user ${userId}`);

    try {
      // Validate files
      if (!files || Object.keys(files).length === 0) {
        throw new Error('No files provided for preview');
      }

      // Get user's fly app name
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { fly_app_name: true },
      });

      if (!user?.fly_app_name) {
        // Fallback to old method if user has no persistent app
        this.logger.log(
          `User ${userId} has no persistent app, falling back to old method`,
        );
        return this.createPreview(files);
      }

      const appName = user.fly_app_name;
      const appUrl = `https://${appName}.fly.dev`;

      // OPTIMIZATION: Just do a quick health check (machines should already be ready from background call)
      this.logger.log(`Performing health check for app: ${appName}`);
      try {
        await this.flyManager.performHealthCheck(appUrl);
        this.logger.log(`✅ App ${appName} is healthy and ready`);
      } catch (healthError) {
        this.logger.warn(
          `App health check failed, but continuing: ${healthError.message}`,
        );
        // Continue anyway - the app might still work for code injection
      }

      // Get machine ID from database (single machine per user)
      const currentMachine = await this.getCurrentUserMachine(userId);
      const machineId = currentMachine?.machineId || 'unknown';

      // Step 2: Inject code into running machine (fast operation)
      this.logger.log('Injecting code into machine...');
      await this.flyManager.injectCode(appUrl, files);
      this.logger.log('Code injection completed successfully');

      // Step 3: Schedule suspension after 2 minutes
      setTimeout(
        () => {
          this.suspendMachine(appName, machineId).catch((error) => {
            this.logger.error(`Failed to suspend machine ${machineId}:`, error);
          });
        },
        parseInt(process.env.PREVIEW_TIMEOUT || '120000'),
      );

      const result = {
        serviceId: machineId, // For compatibility with existing frontend
        appId: appName,
        previewUrl: appUrl,
        wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
        filesCount: Object.keys(files).length,
        provider: 'fly',
        runtimeErrors: [],
        hasRuntimeErrors: false,
      };

      this.logger.log(
        `✅ Optimized preview created successfully: ${result.previewUrl}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to create optimized preview for user ${userId}:`,
        error,
      );

      // Fallback to old method if optimized method fails
      this.logger.log('Falling back to old preview creation method');
      return this.createPreview(files);
    }
  }

  async healthCheck() {
    return {
      status: 'ok',
      service: 'fly-preview',
      timestamp: new Date().toISOString(),
    };
  }

  // =================== NEW MACHINE MANAGEMENT METHODS ===================

  /**
   * Suspend a machine to save resources
   */
  async suspendMachine(appName: string, machineId: string): Promise<void> {
    this.logger.log(`Suspending machine ${machineId} in app ${appName}`);
    return this.flyManager.suspendMachine(appName, machineId);
  }

  /**
   * Health check and reactivate machine if needed (single machine approach)
   */
  async healthCheckAndReactivate(appName: string): Promise<any> {
    this.logger.log(`Health check and reactivation for app ${appName}`);

    try {
      // Get user's machine for the app
      const machine = await this.flyManager.getUserMachine(appName);

      if (!machine) {
        return {
          status: 'no_machine',
          message: 'No machine found in app',
          appName,
        };
      }

      // Check if machine is suspended and start it
      if (machine.state === 'suspended') {
        this.logger.log(`Found suspended machine ${machine.id}, starting it`);

        await this.flyManager.startMachine(appName, machine.id);

        // Wait a bit for machine to start
        await new Promise((resolve) => setTimeout(resolve, 3000));
      }

      // Perform health check on the app
      const appUrl = `https://${appName}.fly.dev`;

      try {
        await this.flyManager.performHealthCheck(appUrl);
        return {
          status: 'healthy',
          message: 'App is healthy and ready',
          appName,
          appUrl,
          machineRestarted: machine.state === 'suspended',
        };
      } catch (healthError) {
        return {
          status: 'unhealthy',
          message: 'App health check failed',
          appName,
          error: healthError.message,
        };
      }
    } catch (error) {
      this.logger.error(`Health check failed for app ${appName}:`, error);
      throw error;
    }
  }

  /**
   * Cleanup user machine (called on logout) - SINGLE MACHINE APPROACH
   */
  async cleanupUserMachine(userId: number): Promise<void> {
    this.logger.log(`Cleaning up machine for user ${userId}`);

    try {
      const machine = await this.getCurrentUserMachine(userId);
      if (machine) {
        try {
          await this.flyManager.deleteMachine(
            machine.appName,
            machine.machineId,
          );
        } catch (error) {
          console.log('Machine already deleted');
        }
        await this.removeMachineTracking(userId);
      }
      this.logger.log(`✅ Cleaned up machine for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup machine for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Suspend user's single machine
   */
  async suspendUserMachine(userId: number): Promise<void> {
    this.logger.log(`Suspending machine for user ${userId}`);

    try {
      const machine = await this.getCurrentUserMachine(userId);
      if (machine && machine.status === 'running') {
        await this.flyManager.suspendMachine(
          machine.appName,
          machine.machineId,
        );
        await this.updateMachineStatus(userId, 'suspended');
        this.logger.log(
          `✅ Suspended machine ${machine.machineId} for user ${userId}`,
        );
      }
    } catch (error) {
      this.logger.error(`Failed to suspend machine for user ${userId}:`, error);
      // Don't throw - suspension failure shouldn't break the flow
    }
  }

  // =================== SINGLE MACHINE MANAGEMENT METHODS ===================

  /**
   * Get user's current machine (only 1 exists)
   */
  async getCurrentUserMachine(userId: number): Promise<any | null> {
    try {
      return await this.prisma.userMachine.findUnique({
        where: { userId },
      });
    } catch (error) {
      this.logger.error(
        `Failed to get current machine for user ${userId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Create/replace user's machine
   */
  async setUserMachine(
    userId: number,
    appName: string,
    machineId: string,
  ): Promise<void> {
    try {
      await this.prisma.userMachine.upsert({
        where: { userId },
        create: {
          userId,
          appName,
          machineId,
          status: 'creating',
        },
        update: {
          machineId,
          appName,
          status: 'creating',
        },
      });
      this.logger.log(`✅ Set machine ${machineId} for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to set machine for user ${userId}:`, error);
      // Don't throw error - tracking failure shouldn't break the flow
    }
  }

  /**
   * Update machine status
   */
  async updateMachineStatus(userId: number, status: string): Promise<void> {
    try {
      await this.prisma.userMachine.update({
        where: { userId },
        data: { status },
      });
      this.logger.log(
        `✅ Updated machine status to ${status} for user ${userId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update machine status for user ${userId}:`,
        error,
      );
      // Don't throw error - tracking failure shouldn't break the flow
    }
  }

  /**
   * Remove machine from tracking
   */
  async removeMachineTracking(userId: number): Promise<void> {
    try {
      await this.prisma.userMachine.delete({
        where: { userId },
      });
      this.logger.log(`✅ Removed machine tracking for user ${userId}`);
    } catch (error) {
      this.logger.error(
        `Failed to remove machine tracking for user ${userId}:`,
        error,
      );
      // Don't throw error - tracking failure shouldn't break the flow
    }
  }

  /**
   * Create machine for user's existing app - SINGLE MACHINE LOGIC
   */
  async createMachineForUser(userId: number): Promise<any> {
    this.logger.log(`Creating/reactivating machine for user ${userId}`);

    try {
      // Get user's fly app name
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { fly_app_name: true },
      });

      if (!user?.fly_app_name) {
        throw new Error(`User ${userId} has no fly app configured`);
      }

      const appName = user.fly_app_name;
      const appUrl = `https://${appName}.fly.dev`;

      // Check if user has a current machine
      const currentMachine = await this.getCurrentUserMachine(userId);

      if (!currentMachine) {
        // No machine exists - create new one
        this.logger.log(
          `No machine exists, creating new one for user ${userId}`,
        );
        const machine =
          await this.flyManager.createMachineInExistingApp(appName);
        await this.setUserMachine(userId, appName, machine.id);
        await this.updateMachineStatus(userId, 'running');

        return {
          machineId: machine.id,
          appName,
          appUrl,
          wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
          message: 'New machine created successfully',
        };
      }

      // Machine exists - check its actual state on Fly
      try {
        const machine = await this.flyManager.getMachine(
          appName,
          currentMachine.machineId,
        );

        if (machine.state === 'suspended') {
          // Reactivate suspended machine
          this.logger.log(
            `Reactivating suspended machine ${currentMachine.machineId}`,
          );
          await this.flyManager.startMachine(appName, currentMachine.machineId);
          await this.updateMachineStatus(userId, 'running');

          return {
            machineId: currentMachine.machineId,
            appName,
            appUrl,
            wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
            message: 'Machine reactivated successfully',
          };
        }

        if (machine.state === 'started') {
          // Already running - use it
          this.logger.log(
            `Using existing running machine ${currentMachine.machineId}`,
          );
          await this.updateMachineStatus(userId, 'running');

          return {
            machineId: currentMachine.machineId,
            appName,
            appUrl,
            wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
            message: 'Using existing running machine',
          };
        }

        // Machine is in bad state - replace it
        this.logger.log(
          `Machine in bad state (${machine.state}), replacing...`,
        );
        const newMachine =
          await this.flyManager.createMachineInExistingApp(appName);
        await this.setUserMachine(userId, appName, newMachine.id);
        await this.updateMachineStatus(userId, 'running');

        return {
          machineId: newMachine.id,
          appName,
          appUrl,
          wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
          message: 'Machine replaced successfully',
        };
      } catch (error) {
        // Machine doesn't exist on Fly anymore - create new one
        this.logger.log(
          `Machine ${currentMachine.machineId} not found on Fly, creating new one`,
        );
        const newMachine =
          await this.flyManager.createMachineInExistingApp(appName);
        await this.setUserMachine(userId, appName, newMachine.id);
        await this.updateMachineStatus(userId, 'running');

        return {
          machineId: newMachine.id,
          appName,
          appUrl,
          wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
          message: 'New machine created successfully',
        };
      }
    } catch (error) {
      this.logger.error(`Failed to create machine for user ${userId}:`, error);
      throw error;
    }
  }
}
