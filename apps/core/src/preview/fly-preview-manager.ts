import {
  FlyMachine,
  FlyCreateAppResponse,
  FlyCreateMachineRequest,
  FlyWaitResponse,
  FlyPreviewResult,
  FlyCreateAppRequest,
  FlyAppDeleteParams,
  FLY_MACHINE_PRESETS,
  FLY_REGIONS,
  FlyAllocateIpResponse,
  FlyLogsResponse,
  RuntimeError,
} from './fly-types';
import {
  TWO_MINUTES_MS,
  HEALTH_CHECK_INTERVAL_MS,
  APP_PORT,
  INJECT_CODE_PATH,
  HEALTH_CHECK_PATH,
} from './constants';

export class FlyPreviewManager {
  private apiKey: string;
  private baseUrl = 'https://api.machines.dev';
  private graphqlUrl = 'https://api.fly.io/graphql';
  private dockerImage: string;
  private orgSlug: string;

  constructor(apiKey: string, dockerImage: string, orgSlug: string) {
    this.apiKey = apiKey;
    this.dockerImage = dockerImage;
    this.orgSlug = orgSlug;
  }

  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    body?: any,
    options?: { baseUrl?: string; headers?: Record<string, string> },
  ): Promise<T> {
    try {
      const baseUrl = options?.baseUrl || this.baseUrl;
      const defaultHeaders = {
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      };
      const headers = { ...defaultHeaders, ...options?.headers };

      const response = await fetch(`${baseUrl}${endpoint}`, {
        method,
        headers,
        body: body ? JSON.stringify(body) : undefined,
      });

      const responseText = await response.text();

      if (!response.ok) {
        console.error(`Fly.io API error: ${response.status} - ${responseText}`);
        throw new Error(
          `Fly.io API error: ${response.status} - ${responseText}`,
        );
      }

      // Handle empty responses (common with DELETE operations)
      if (!responseText || responseText.trim() === '') {
        return {} as T;
      }

      try {
        return JSON.parse(responseText);
      } catch (error) {
        console.error('Failed to parse response as JSON:', responseText);

        // If it's a timeout or other error message, provide more context
        if (
          responseText.includes('TimeoutError') ||
          responseText.includes('timeout')
        ) {
          throw new Error(`Request timeout: ${responseText}`);
        }

        throw new Error(
          `Failed to parse Fly.io API response: ${error.message}. Response: ${responseText}`,
        );
      }
    } catch (error) {
      console.error(`Request to ${endpoint} failed:`, error);
      throw error;
    }
  }

  async createApp(appName: string): Promise<FlyCreateAppResponse> {
    const payload: FlyCreateAppRequest = {
      app_name: appName,
      org_slug: this.orgSlug,
    };

    return this.makeRequest<FlyCreateAppResponse>('/v1/apps', 'POST', payload);
  }

  async allocateSharedIPv4(appName: string): Promise<string> {
    try {
      console.log(`Allocating shared IPv4 for app: ${appName}`);

      const query = `
        mutation ($input: AllocateIPAddressInput!) {
          allocateIpAddress(input: $input) {
            ipAddress {
              address
            }
          }
        }
      `;

      const variables = {
        input: {
          appId: appName,
          type: 'shared_v4',
        },
      };

      const response = await this.makeRequest<FlyAllocateIpResponse>(
        '',
        'POST',
        {
          query,
          variables,
        },
        {
          baseUrl: this.graphqlUrl,
        },
      );

      if (response.errors?.length) {
        throw new Error(`GraphQL error: ${JSON.stringify(response.errors)}`);
      }

      console.log(`✅ Shared IPv4 allocated`);
      return 'success';
    } catch (error) {
      console.error('❌ IPv4 allocation failed:', error);
      throw error;
    }
  }

  async createPreviewApp(): Promise<FlyPreviewResult> {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 11);
    const appName = `preview-app-${timestamp}-${randomId}`;
    const machineName = `preview-machine-${timestamp}-${randomId}`;

    // Step 1: Create App (top-level container)
    const app = await this.createApp(appName);

    // Step 2: Create Machine inside the App
    const machineConfig: FlyCreateMachineRequest = {
      name: machineName,
      config: {
        guest: FLY_MACHINE_PRESETS.SMALL,
        image: this.dockerImage,
        restart: {
          policy: 'no',
        },
        auto_destroy: true,
        services: [
          {
            ports: [
              {
                port: 80,
                handlers: ['http'],
              },
              {
                port: 443,
                handlers: ['tls', 'http'],
              },
            ],
            protocol: 'tcp',
            internal_port: APP_PORT,
          },
        ],
        env: {
          NODE_ENV: 'development',
          PORT: APP_PORT.toString(),
        },
        metadata: {
          'preview-app': 'true',
          'created-at': new Date().toISOString(),
          timeout: TWO_MINUTES_MS.toString(),
        },
      },
      region: FLY_REGIONS.SINGAPORE,
      skip_launch: false,
      skip_service_registration: false,
    };

    try {
      const machine = await this.makeRequest<FlyMachine>(
        `/v1/apps/${appName}/machines`,
        'POST',
        machineConfig,
      );

      // Step 3: Wait for the machine to be ready
      await this.waitForMachineReady(appName, machine.id);

      const appUrl = `https://${appName}.fly.dev`;

      // Step 4: Allocate shared IPv4 address for public accessibility
      await this.allocateSharedIPv4(appName);

      // Step 5: Perform additional health check after IP allocation
      try {
        await this.performHealthCheck(appUrl);
        console.log(`✅ Health check passed for app ${appName} (${appUrl})`);
      } catch (error) {
        console.warn(
          `Health check failed for app ${appName} (${appUrl}). App may not be fully ready: ${error.message}`,
        );
        // Continue anyway - the app might still work
      }

      // Schedule auto-destruction after 2 minutes
      setTimeout(
        () => {
          this.destroyApp(appName);
        },
        parseInt(process.env.PREVIEW_TIMEOUT || TWO_MINUTES_MS.toString()),
      );

      return {
        machineId: machine.id,
        appId: app.id,
        appUrl,
        appName,
      };
    } catch (error) {
      console.error('Machine creation failed:', error);

      // Clean up the app if machine creation fails
      try {
        await this.destroyApp(appName);
        console.log(`App ${appName} deleted after machine creation failure`);
      } catch (cleanupError) {
        console.error(
          `Failed to clean up app ${appName}:`,
          cleanupError.message || cleanupError,
        );
        // Don't re-throw cleanup errors - the original error is more important
      }

      throw error;
    }
  }

  async getMachine(appName: string, machineId: string): Promise<FlyMachine> {
    return this.makeRequest<FlyMachine>(
      `/v1/apps/${appName}/machines/${machineId}`,
    );
  }

  async waitForMachineReady(appName: string, machineId: string): Promise<void> {
    console.log(`Waiting for machine ${machineId} to be ready...`);

    try {
      // Use server-side long polling with 120 second timeout
      // The server will hold the connection open until the machine is ready or timeout
      await this.makeRequest<FlyWaitResponse>(
        `/v1/apps/${appName}/machines/${machineId}/wait?state=started&timeout=30`,
      );

      console.log(`✅ Machine ${machineId} is ready and started`);
      return;
    } catch (error) {
      // Handle specific response codes from the /wait endpoint
      if (error.message?.includes('408')) {
        // Request timeout - machine didn't reach 'started' state within 120s
        console.error(
          `❌ Machine ${machineId} failed to start within 120 seconds`,
        );
        throw new Error('Machine failed to start within timeout period');
      } else if (error.message?.includes('400')) {
        // Bad request - invalid state or machine ID
        console.error(`❌ Invalid wait request for machine ${machineId}`);
        throw new Error('Invalid machine ID or state parameter');
      } else {
        // Other errors (network, auth, etc.)
        console.error(
          `❌ Unexpected error waiting for machine ${machineId}:`,
          error,
        );
        throw new Error(`Failed to wait for machine: ${error.message}`);
      }
    }
  }

  async performHealthCheck(appUrl: string): Promise<void> {
    const healthUrl = `${appUrl}${HEALTH_CHECK_PATH}`;
    let attempts = 0;
    const maxAttempts = 60; // 30 seconds (60 * 500ms) - increased for slower app startup

    console.log(`Starting health check for ${healthUrl}`);

    while (attempts < maxAttempts) {
      try {
        const response = await fetch(healthUrl, {
          method: 'GET',
          signal: AbortSignal.timeout(5000), // 5 second timeout
        });

        if (response.status === 200) {
          console.log('✅ Health check passed');
          return;
        } else {
          console.log(
            `Health check attempt ${attempts + 1}: received status ${response.status}`,
          );
        }
      } catch (error) {
        // Handle different types of errors without JSON parsing
        const errorMessage =
          error.name === 'TimeoutError'
            ? 'Request timeout'
            : error.message || 'Unknown error';
        console.log(
          `Health check attempt ${attempts + 1} failed: ${errorMessage}`,
        );
      }

      await new Promise((resolve) =>
        setTimeout(resolve, HEALTH_CHECK_INTERVAL_MS),
      );
      attempts++;
    }

    throw new Error('Health check failed within timeout');
  }

  async injectCode(
    appUrl: string,
    files: Record<string, string>,
  ): Promise<void> {
    try {
      const injectionUrl = `${appUrl}${INJECT_CODE_PATH}`;

      console.log(`Attempting code injection to: ${injectionUrl}`);

      const response = await fetch(injectionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ files }),
        signal: AbortSignal.timeout(30000), // 30 second timeout
      });

      if (!response.ok) {
        throw new Error(`Code injection failed: ${response.status}`);
      }

      const result = await response.json();
      console.log('Code injection successful:', result.message);
    } catch (e) {
      console.error('Code injection failed:', e);
      throw e;
    }
  }

  // =================== NEW MACHINE MANAGEMENT METHODS ===================

  /**
   * Suspend a machine to save resources
   */
  async suspendMachine(appName: string, machineId: string): Promise<void> {
    try {
      console.log(`Suspending machine ${machineId} in app ${appName}`);
      await this.makeRequest(
        `/v1/apps/${appName}/machines/${machineId}/suspend`,
        'POST',
      );
      console.log(`✅ Machine ${machineId} suspended successfully`);
    } catch (error) {
      console.error(`❌ Failed to suspend machine ${machineId}:`, error);
      throw error;
    }
  }

  /**
   * Start a suspended machine
   */
  async startMachine(appName: string, machineId: string): Promise<void> {
    try {
      console.log(`Starting machine ${machineId} in app ${appName}`);
      await this.makeRequest(
        `/v1/apps/${appName}/machines/${machineId}/start`,
        'POST',
      );
      console.log(`✅ Machine ${machineId} started successfully`);
    } catch (error) {
      console.error(`❌ Failed to start machine ${machineId}:`, error);
      throw error;
    }
  }

  /**
   * Get user's machine for an app (single machine approach)
   */
  async getUserMachine(appName: string): Promise<FlyMachine | null> {
    try {
      console.log(`Getting user machine for app ${appName}`);
      const machines = await this.makeRequest<FlyMachine[]>(
        `/v1/apps/${appName}/machines`,
      );
      const machine = machines.length > 0 ? machines[0] : null;
      console.log(`✅ Found ${machine ? '1' : '0'} machine in app ${appName}`);
      return machine;
    } catch (error) {
      console.error(`❌ Failed to get user machine for app ${appName}:`, error);
      throw error;
    }
  }

  /**
   * Create a machine in an existing app (for reusing persistent apps)
   */
  async createMachineInExistingApp(appName: string): Promise<FlyMachine> {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 11);
    const machineName = `preview-machine-${timestamp}-${randomId}`;

    const machineConfig: FlyCreateMachineRequest = {
      name: machineName,
      config: {
        guest: FLY_MACHINE_PRESETS.SMALL,
        image: this.dockerImage,
        restart: {
          policy: 'no',
        },
        auto_destroy: true,
        services: [
          {
            ports: [
              {
                port: 80,
                handlers: ['http'],
              },
              {
                port: 443,
                handlers: ['tls', 'http'],
              },
            ],
            protocol: 'tcp',
            internal_port: APP_PORT,
          },
        ],
        env: {
          NODE_ENV: 'development',
          PORT: APP_PORT.toString(),
        },
        metadata: {
          'preview-machine': 'true',
          'created-at': new Date().toISOString(),
        },
      },
      region: FLY_REGIONS.SINGAPORE,
      skip_launch: false,
      skip_service_registration: false,
    };

    try {
      console.log(`Creating machine in existing app: ${appName}`);
      const machine = await this.makeRequest<FlyMachine>(
        `/v1/apps/${appName}/machines`,
        'POST',
        machineConfig,
      );

      // Wait for the machine to be ready
      await this.waitForMachineReady(appName, machine.id);
      console.log(
        `✅ Machine ${machine.id} created and ready in app ${appName}`,
      );

      return machine;
    } catch (error) {
      console.error(`❌ Failed to create machine in app ${appName}:`, error);
      throw error;
    }
  }

  /**
   * Delete user's machine in an app (single machine approach)
   */
  async deleteUserMachine(appName: string): Promise<void> {
    try {
      const machine = await this.getUserMachine(appName);

      if (!machine) {
        console.log(`No machine to delete in app ${appName}`);
        return;
      }

      console.log(`Deleting machine ${machine.id} in app ${appName}`);

      try {
        await this.makeRequest(
          `/v1/apps/${appName}/machines/${machine.id}`,
          'DELETE',
        );
        console.log(`✅ Deleted machine ${machine.id}`);
      } catch (error) {
        console.error(`❌ Failed to delete machine ${machine.id}:`, error);
        throw error;
      }

      console.log(`✅ Machine deleted from app ${appName}`);
    } catch (error) {
      console.error(`❌ Failed to delete machine from app ${appName}:`, error);
      throw error;
    }
  }

  /**
   * Monitor runtime errors in app logs
   */
  async monitorRuntimeErrors(appName: string): Promise<RuntimeError[]> {
    try {
      console.log(`Monitoring runtime errors for app: ${appName}`);

      // Wait for application to process injected code
      await new Promise((resolve) => setTimeout(resolve, 10000));

      const query = `
        query GetAppLogs($appName: String!, $limit: Int!, $range: Int!) {
          app(name: $appName) {
            allocations {
              recentLogs(limit: $limit, range: $range) {
                id
                instanceId
                level
                message
                region
                timestamp
              }
            }
          }
        }
      `;

      const variables = {
        appName,
        limit: 30,
        range: 60,
      };

      const response = await this.makeRequest<{
        data: {
          app: {
            allocations: Array<{
              recentLogs: Array<{
                id: string;
                instanceId: string;
                level: string;
                message: string;
                region: string;
                timestamp: string;
              }>;
            }>;
          };
        };
        errors?: Array<{ message: string }>;
      }>('', 'POST', { query, variables }, { baseUrl: this.graphqlUrl });

      if (response.errors?.length) {
        throw new Error(`GraphQL error: ${JSON.stringify(response.errors)}`);
      }

      // Flatten logs from all allocations
      const allLogs = response.data.app.allocations.flatMap(
        (allocation) => allocation.recentLogs,
      );
      // const errorKeywords = [
      //   'error',
      //   'exception',
      //   'failed',
      //   'crash',
      //   'cannot',
      //   'undefined',
      //   'null',
      //   'syntaxerror',
      //   'referenceerror',
      //   'typeerror',
      //   'uncaught',
      //   'unhandled',
      // ];

      // const runtimeErrors = logs
      //   .filter((log) => {
      //     const message = log.message.toLowerCase();
      //     return (
      //       errorKeywords.some((keyword) => message.includes(keyword)) ||
      //       log.level.toLowerCase() === 'error'
      //     );
      //   })
      //   .map((log) => ({
      //     message: log.message,
      //     level: log.level,
      //     timestamp: log.timestamp,
      //     region: log.region,
      //     instanceId: log.instanceId,
      //   }));

      // console.log(
      //   runtimeErrors.length > 0
      //     ? `❌ Found ${runtimeErrors.length} runtime errors`
      //     : '✅ No runtime errors detected',
      // );

      // return runtimeErrors;

      // For testing: return the structured logs as RuntimeError objects
      if (allLogs && allLogs.length > 0) {
        return allLogs.map((log) => ({
          message: log.message,
          level: log.level,
          timestamp: log.timestamp,
          region: log.region,
          instanceId: log.instanceId,
        }));
      }

      return [];
    } catch (error) {
      console.error('❌ Failed to monitor runtime errors:', error);
      return [];
    }
  }

  async destroyApp(appName: string): Promise<void> {
    try {
      const deleteParams: FlyAppDeleteParams = { force: true };

      // Delete the app - this will automatically delete all associated machines
      await this.makeRequest(`/v1/apps/${appName}`, 'DELETE', deleteParams);
      console.log(
        `App ${appName} deleted successfully (machines auto-deleted)`,
      );
    } catch (error) {
      console.error(`Failed to destroy app ${appName}:`, error);
    }
  }

  async stopMachine(appName: string, machineId: string): Promise<void> {
    try {
      await this.makeRequest(
        `/v1/apps/${appName}/machines/${machineId}/stop`,
        'POST',
        { timeout: '30s' },
      );
      console.log(`Machine ${machineId} stopped successfully`);
    } catch (error) {
      console.error(`Failed to stop machine ${machineId}:`, error);
    }
  }

  async deleteMachine(appName: string, machineId: string): Promise<void> {
    try {
      await this.makeRequest(
        `/v1/apps/${appName}/machines/${machineId}`,
        'DELETE',
        { force: true },
      );
      console.log(`Machine ${machineId} deleted successfully`);
    } catch (error) {
      console.error(`Failed to delete machine ${machineId}:`, error);
    }
  }
}
